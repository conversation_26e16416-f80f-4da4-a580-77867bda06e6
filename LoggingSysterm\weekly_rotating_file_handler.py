import logging
import os
import datetime
import shutil

class WeeklyRotatingFileHandler(logging.FileHandler):
    def __init__(self, filename, mode='a', encoding=None, delay=False):
        self.baseFilename = filename
        self.current_day_dir = self._get_current_day_dir()
        self._ensure_log_directory_exists()
        self._delete_old_log_directories()
        super().__init__(os.path.join(self.current_day_dir, os.path.basename(filename)), mode, encoding, delay)

    def _get_current_day_dir(self):
        today = datetime.date.today()
        day_name = today.strftime('%A').lower()  # e.g., 'monday', 'tuesday'
        date_str = today.strftime('%d_%m_%Y')  # e.g., '10_08_2025'
        log_dir = os.path.dirname(self.baseFilename) or 'logs'
        return os.path.join(log_dir, f"{day_name}_{date_str}")

    def _ensure_log_directory_exists(self):
        os.makedirs(self.current_day_dir, exist_ok=True)

    def _delete_old_log_directories(self):
        # Get the root log directory (e.g., 'logs')
        root_log_dir = os.path.dirname(self.baseFilename) or 'logs'
        if not os.path.exists(root_log_dir):
            return

        today = datetime.date.today()
        current_day_name = today.strftime('%A').lower()


        now = datetime.datetime.now()
        seven_days_ago = now - datetime.timedelta(days=7)

        # Find and delete directories for the current day of the week that are older than a week
        for item in os.listdir(root_log_dir):
            if item.startswith(current_day_name + '_'):
                item_path = os.path.join(root_log_dir, item)
                if os.path.isdir(item_path) and item != self.current_day_dir.split(os.path.sep)[-1]:
                    print(f"Deleting old log directory for {current_day_name}: {item_path}")
                    shutil.rmtree(item_path)

    def emit(self, record):
        # Check if the day has changed
        new_day_dir = self._get_current_day_dir()
        if new_day_dir != self.current_day_dir:
            self.current_day_dir = new_day_dir
            self._ensure_log_directory_exists()
            self._delete_old_log_directories()
            self.stream.close()
            self.baseFilename = os.path.join(self.current_day_dir, os.path.basename(self.baseFilename))
            self.stream = self._open()
        super().emit(record)