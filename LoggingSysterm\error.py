# 9/1
import io
import json
import logging
import os
import threading

import requests
from dotenv import load_dotenv

from weekly_rotating_file_handler import WeeklyRotatingFileHandler

load_dotenv()
TELEGRAM_DEFAULT_CHANNEL_ID = os.getenv("TG_CHANNEL_ID")
SERVER_ID = os.getenv("SERVER_ID")
TG_BOT_TOKEN = os.getenv("TG_BOT_TOKEN")
ERROR_CONFIG = {
    "telegram levels": ["error", "warning"],
}
logger_tg_channels = {}


def telegram_wrapper(func):
    """
    Wraps a function to send a telegram message if the function raises an exception
    and run the function in a thread without waiting for it to finish
    """

    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.start()

    return wrapper


@telegram_wrapper
def send_telegram_message(message, channel=TELEGRAM_DEFAULT_CHANNEL_ID):
    url = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendMessage"
    payload = {"chat_id": channel, "text": message[:4000]}
    response = requests.post(url, json=payload)
    if response.status_code > 299:
        raise Exception(response.text[:100])
    return response.json()


@telegram_wrapper
def send_telegram_photo(message, image_bytes, channel=TELEGRAM_DEFAULT_CHANNEL_ID):
    url_photo = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendPhoto"
    with io.BytesIO(image_bytes) as image_file:
        payload_photo = {"chat_id": channel, "caption": message[:4000]}
        files = {"photo": image_file}
        response_photo = requests.post(url_photo, data=payload_photo, files=files)
        if response_photo.status_code > 299:
            raise Exception(response_photo.text[:100])
    return response_photo.json()


@telegram_wrapper
def send_telegram_file(
    message, file_data, filename="data.json", channel=TELEGRAM_DEFAULT_CHANNEL_ID
):
    url_document = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendDocument"

    # Handle different input types (dict, string, or bytes)
    if isinstance(file_data, dict):
        file_bytes = json.dumps(file_data, ensure_ascii=False, indent=4).encode("utf-8")
    elif isinstance(file_data, str):
        file_bytes = file_data.encode("utf-8")
    elif isinstance(file_data, bytes):
        file_bytes = file_data
    else:
        raise TypeError("file_data must be dict, str, or bytes")

    with io.BytesIO(file_bytes) as file_obj:
        payload = {"chat_id": channel, "caption": message[:1000]}
        files = {"document": (filename, file_obj, "application/json")}
        response = requests.post(url_document, data=payload, files=files)
        if response.status_code > 299:
            raise Exception(response.text[:100])
    return response.json()


def setup_logger(
    name, log_file, tg_channel=TELEGRAM_DEFAULT_CHANNEL_ID, level=logging.INFO
):
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger_tg_channels[name] = tg_channel
    if not logger.handlers:
        # Use WeeklyRotatingFileHandler instead of logging.FileHandler
        file_handler = WeeklyRotatingFileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(level)
        stream = logging.StreamHandler()

        class CustomFormatter(logging.Formatter):
            def format(self, record):
                if not hasattr(record, "context"):
                    record.context = ""
                return super().format(record)

        formatter = CustomFormatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s - %(context)s"
        )

        file_handler.setFormatter(formatter)
        stream.setFormatter(formatter)

        logger.addHandler(file_handler)
        logger.addHandler(stream)
    return logger


if not os.path.exists("logs"):
    os.makedirs("logs")
if not os.path.exists("xml_log"):
    os.makedirs("xml_log")
if not os.path.exists("screenshots"):
    os.makedirs("screenshots")


def log(
    logger: logging.Logger,
    level,
    text,
    send_on_telegram=True,
    screenshot: bytes | None = None,
    exc_info: bool = True,
    **context,
):
    # Add SERVER_ID to the text message
    server_prefix = f"[SERVER:{SERVER_ID}] " if SERVER_ID else ""
    prefixed_text = f"{server_prefix}{text}"

    if (
        send_on_telegram
        and level.lower() in ERROR_CONFIG["telegram levels"]
        or screenshot
        or "file" in context
    ):
        try:
            t = f"{server_prefix}{logger.name} :\n{level}\n{text}\n"
            file = None
            if context:
                file = context.pop("file", None)
                t += f"{json.dumps(context, ensure_ascii=False, indent=4)}"
            # Handle different types of attachments
            if file:
                try:
                    filename = context.get("filename", "data.json")
                    send_telegram_file(
                        t,
                        file,
                        filename=filename,
                        channel=logger_tg_channels[logger.name],
                    )
                except Exception as e:
                    send_telegram_message(
                        t + "\nfile couldn\'t be sent\n" + str(e),
                        channel=logger_tg_channels[logger.name],
                    )
            elif screenshot is not None:
                try:
                    send_telegram_photo(
                        t, screenshot, channel=logger_tg_channels[logger.name]
                    )
                except Exception as e:
                    send_telegram_message(
                        t + "\nscreenshot couldn\'t be sent\n" + str(e),
                        channel=logger_tg_channels[logger.name],
                    )
            else:
                send_telegram_message(t, channel=logger_tg_channels[logger.name])
        except Exception as e:
            log(logger, "info", "couldn\'t send on telegram\n" + str(e), False)
    if hasattr(logger, level.lower()):
        method = getattr(logger, level.lower())
    else:
        method = logger.info
    if screenshot:
        context["with_photo"] = True
    if "file" in context:
        context["with_file"] = True

    method(prefixed_text + "\n", extra={"context": context}, exc_info=exc_info)