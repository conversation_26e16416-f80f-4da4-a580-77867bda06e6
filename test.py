class ShoppingList:
    def __init__(self, items):
        self.items = items

    def __len__(self):
        return len(self.items)

    def __getitem__(self, index):
        try:
            return self.items[index]

        except:
            return "Item not found"


s = ShoppingList(["apple", "banana", "milk"])

print (len(s))
print(s[4])

"""
“__contains__” method is a built-in method:
that allows us to check whether a specific element exists in a collection or not. 
It is used with the “in” keyword to test if an object is contained within another object.
"""

class MyList:
    def __init__(self, data):
        self.data = data

    def __contains__(self, item):
        return item in self.data

my_list = MyList([1, 2, 3, 4, 5])
print(3 in my_list)   # True
print(6 in my_list)   # False