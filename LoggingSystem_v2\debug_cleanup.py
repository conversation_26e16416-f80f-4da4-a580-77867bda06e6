import os
import datetime
import shutil
from unittest.mock import patch
from daily_rotating_file_handler import DailyRotatingFileHandler

# Create a test directory structure
def create_test_folders():
    # Create logs directory if it doesn't exist
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Create two Sunday folders with different dates
    # First Sunday (older)
    old_sunday = 'logs/sunday_03_08_2025'
    if not os.path.exists(old_sunday):
        os.makedirs(old_sunday)
        with open(os.path.join(old_sunday, 'test.log'), 'w') as f:
            f.write('Old Sunday log')
    
    # Second Sunday (newer)
    new_sunday = 'logs/sunday_10_08_2025'
    if not os.path.exists(new_sunday):
        os.makedirs(new_sunday)
        with open(os.path.join(new_sunday, 'test.log'), 'w') as f:
            f.write('New Sunday log')

# Debug the cleanup logic
def debug_cleanup():
    print('\n--- DEBUG CLEANUP LOGIC ---')
    
    # Define our test dates
    first_sunday = datetime.date(2025, 8, 3)
    second_sunday = datetime.date(2025, 8, 10)
    
    # First, create a handler with the first Sunday date
    print(f"\nInitializing handler with first Sunday date: {first_sunday}")
    
    # Use patch to mock datetime.date.today
    with patch('daily_rotating_file_handler.datetime.date') as mock_date:
        # Configure mock to return first_sunday for today()
        mock_date.today.return_value = first_sunday
        # Copy other attributes from real datetime.date
        mock_date.__eq__ = datetime.date.__eq__
        mock_date.__lt__ = datetime.date.__lt__
        mock_date.__gt__ = datetime.date.__gt__
        mock_date.__le__ = datetime.date.__le__
        mock_date.__ge__ = datetime.date.__ge__
        mock_date.__sub__ = datetime.date.__sub__
        
        # Create handler (this will create directories for the first Sunday)
        handler1 = DailyRotatingFileHandler('logs/test.log')
        
        # Now advance to second Sunday
        print(f"\nAdvancing to second Sunday date: {second_sunday}")
        mock_date.today.return_value = second_sunday
        
        # Create a new handler (this should trigger cleanup of first Sunday)
        handler2 = DailyRotatingFileHandler('logs/test.log')
        
        # Close the file handlers to release the files
        handler1.close()
        
        # For our debug tests, use the second handler
        handler = handler2
        current_date = second_sunday
    
    # List folders before cleanup
    print('\nFolders before manual cleanup:')
    for folder in os.listdir('logs'):
        if os.path.isdir(os.path.join('logs', folder)):
            print(f'  - {folder}')
    
    # Test folder parsing and cleanup decision for each folder
    print('\nTesting folder parsing and cleanup decisions:')
    for folder in os.listdir('logs'):
        folder_path = os.path.join('logs', folder)
        if not os.path.isdir(folder_path):
            continue
        
        day_name, folder_date = handler._parse_folder_date_and_day(folder)
        should_cleanup = handler._should_cleanup_folder(folder, current_date)
        
        print(f'Folder: {folder}')
        print(f'  Parsed day: {day_name}, date: {folder_date}')
        print(f'  Should cleanup: {should_cleanup}')
        
        if day_name and folder_date:
            days_diff = (current_date - folder_date).days
            print(f'  Days difference: {days_diff}')
    
    # Check if automatic cleanup worked
    print('\nChecking if automatic cleanup worked:')
    
    # List folders after automatic cleanup
    folders_after_auto_cleanup = []
    for folder in os.listdir('logs'):
        if os.path.isdir(os.path.join('logs', folder)):
            folders_after_auto_cleanup.append(folder)
    
    print(f"  Folders after automatic cleanup: {folders_after_auto_cleanup}")
    
    # Check if old Sunday folder was deleted
    if 'sunday_03_08_2025' in folders_after_auto_cleanup:
        print("  FAIL: Old Sunday folder was not automatically deleted")
    else:
        print("  SUCCESS: Old Sunday folder was automatically deleted")
    
    # Check if new Sunday folder exists
    if 'sunday_10_08_2025' in folders_after_auto_cleanup:
        print("  SUCCESS: New Sunday folder exists")
    else:
        print("  FAIL: New Sunday folder does not exist")
    
    # Close the handler before trying direct folder deletion
    handler.close()
    
    # Try direct folder deletion
    print('\nTrying direct folder deletion:')
    for folder in os.listdir('logs'):
        folder_path = os.path.join('logs', folder)
        if not os.path.isdir(folder_path):
            continue
        
        should_cleanup = handler._should_cleanup_folder(folder, current_date)
        print(f"Folder: {folder}, Should cleanup: {should_cleanup}")
        
        if should_cleanup:
            print(f"  Attempting to directly delete: {folder_path}")
            try:
                shutil.rmtree(folder_path)
                print(f"  Successfully deleted: {folder_path}")
            except Exception as e:
                print(f"  Failed to delete: {e}")
    
    # Check if folders still exist
    print('\nFolders after direct deletion:')
    for folder in os.listdir('logs'):
        if os.path.isdir(os.path.join('logs', folder)):
            print(f'  - {folder}')
    
    # List folders after cleanup
    print('\nFolders after manual cleanup:')
    for folder in os.listdir('logs'):
        if os.path.isdir(os.path.join('logs', folder)):
            print(f'  - {folder}')

# Run the debug script
if __name__ == '__main__':
    create_test_folders()
    debug_cleanup()