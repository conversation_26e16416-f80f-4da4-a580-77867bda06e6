#!/usr/bin/env python3
"""
Demonstration script for the day-based log rotation system.
This script simulates a full week cycle to show how logs are rotated.
"""

import os
import shutil
import time
from datetime import datetime, timedelta
from unittest.mock import patch

# Import the modified error module
import error


def simulate_day(day_name, date_obj, day_number):
    """Simulate logging activity for a specific day."""
    print(f"\n--- Day {day_number}: {day_name.upper()} ({date_obj.strftime('%Y-%m-%d')}) ---")
    
    with patch('error.datetime') as mock_datetime:
        mock_datetime.now.return_value = date_obj
        mock_datetime.fromtimestamp.side_effect = datetime.fromtimestamp
        
        # Create loggers like the real application
        loggers = {
            'main': error.setup_logger("main", "logs/main.log"),
            'docker': error.setup_logger("docker_restarter", "logs/docker_restarter.log"),
            'task': error.setup_logger("Task", "logs/Task.log"),
            'device': error.setup_logger("device_manager", "logs/device_manager.log"),
        }
        
        # Log some activity
        for name, logger in loggers.items():
            logger.info(f"Starting {name} service on {day_name}")
            logger.info(f"Processing requests on {day_name}")
            if day_number % 3 == 0:  # Simulate some warnings every 3rd day
                logger.warning(f"High load detected on {name} service")
        
        # Check what files exist
        day_dir = os.path.join("logs", day_name)
        if os.path.exists(day_dir):
            files = [f for f in os.listdir(day_dir) if not f.startswith('.')]
            print(f"Log files in {day_dir}: {files}")
            
            # Show file sizes
            for file in files:
                file_path = os.path.join(day_dir, file)
                if os.path.isfile(file_path):
                    size = os.path.getsize(file_path)
                    print(f"  {file}: {size} bytes")
        else:
            print(f"Directory {day_dir} does not exist")


def show_directory_structure():
    """Display the current logs directory structure."""
    print("\n" + "="*60)
    print("CURRENT LOGS DIRECTORY STRUCTURE:")
    print("="*60)
    
    if not os.path.exists("logs"):
        print("No logs directory exists")
        return
    
    for root, dirs, files in os.walk("logs"):
        level = root.replace("logs", "").count(os.sep)
        indent = " " * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        
        subindent = " " * 2 * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            size = os.path.getsize(file_path)
            if file.startswith('.'):
                print(f"{subindent}{file} (cleanup marker)")
            else:
                print(f"{subindent}{file} ({size} bytes)")


def main():
    """Run the demonstration."""
    print("DAY-BASED LOG ROTATION DEMONSTRATION")
    print("="*60)
    print("This demo simulates a full week + 1 day to show log rotation")
    print("When Sunday comes again, the old Sunday logs should be deleted")
    
    # Clean up any existing logs
    if os.path.exists("logs"):
        shutil.rmtree("logs")
        print("\nCleaned up existing logs directory")
    
    # Start from a Sunday
    start_date = datetime(2024, 1, 7, 10, 0, 0)  # A Sunday
    days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
    
    # Simulate 8 days (full week + 1 to show rotation)
    for i, day_name in enumerate(days):
        current_date = start_date + timedelta(days=i)
        simulate_day(day_name, current_date, i + 1)
        
        if i == 0:  # After first Sunday
            show_directory_structure()
            print(f"\n📝 Created initial Sunday logs")
        
        elif i == 6:  # After Saturday (before second Sunday)
            show_directory_structure()
            print(f"\n📝 Full week completed - all 7 days have logs")
        
        elif i == 7:  # After second Sunday
            show_directory_structure()
            print(f"\n🔄 ROTATION OCCURRED! Old Sunday logs were deleted, new Sunday logs created")
    
    print("\n" + "="*60)
    print("DEMONSTRATION COMPLETED")
    print("="*60)
    print("Key observations:")
    print("1. Each day creates its own directory under logs/")
    print("2. Log files are organized by day of the week")
    print("3. When Sunday comes again (after 7 days), old Sunday logs are deleted")
    print("4. This ensures logs are kept for exactly one week")
    print("5. Each day directory contains a .last_cleanup marker file")


def test_error_handling():
    """Test error handling scenarios."""
    print("\n" + "="*60)
    print("TESTING ERROR HANDLING")
    print("="*60)
    
    # Test with permission issues (simulated)
    print("Testing graceful handling of cleanup errors...")
    
    # Create a test directory structure
    test_day = "monday"
    day_dir = os.path.join("logs", test_day)
    os.makedirs(day_dir, exist_ok=True)
    
    # Create some test files
    test_files = ["test1.log", "test2.log", "test3.log"]
    for file in test_files:
        with open(os.path.join(day_dir, file), 'w') as f:
            f.write(f"Test content for {file}")
    
    print(f"Created test files: {test_files}")
    
    # Test cleanup function
    try:
        error.cleanup_old_logs(day_dir)
        print("✓ Cleanup completed successfully")
        
        # Check what remains
        remaining_files = os.listdir(day_dir)
        print(f"Files after cleanup: {remaining_files}")
        
    except Exception as e:
        print(f"✗ Cleanup failed: {e}")


if __name__ == "__main__":
    main()
    test_error_handling()
    
    print(f"\nDemo completed. Check the 'logs' directory to see the structure.")
    print("You can run this script multiple times to see how it maintains the weekly rotation.")
