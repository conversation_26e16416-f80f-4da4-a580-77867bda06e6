# test_logging.py
import os
import shutil
import tempfile
import unittest
from datetime import datetime, timedelta, date
from unittest.mock import patch, MagicMock
import time
import logging

# Import your original logging system components
from error import setup_logger, log, DailyRotatingFileHandler
from daily_rotating_file_handler import DailyRotatingFileHandler as DRFH


class MockDate(date):
    """Mock date class that allows us to control what today() returns"""
    @classmethod
    def today(cls):
        return cls._today_value

class TestLoggingSystem(unittest.TestCase):
    def setUp(self):
        """Set up test environment with temporary directory"""
        
        custom_save_logs_dir = True
        # To save in Local\Temp:
        if not custom_save_logs_dir:
            self.test_dir = tempfile.mkdtemp()
        
        # To save in the current workspace or specified directory
        else:
            # Create temp dir within current workspace
            workspace_path = "c:/Users/<USER>/OneDrive/Desktop/android_project/testing"
            test_temp_dir = os.path.join(workspace_path, "test_temp")
            os.makedirs(test_temp_dir, exist_ok=True)  # Ensure parent dir exists
            self.test_dir = tempfile.mkdtemp(dir=test_temp_dir)
        
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)

        # Create required directories
        os.makedirs("logs", exist_ok=True)
        os.makedirs("xml_log", exist_ok=True)
        os.makedirs("screenshots", exist_ok=True)

        # Set up date/time mocking
        self.current_time = datetime(2025, 8, 10)  # Default to Sunday
        MockDate._today_value = self.current_time.date()

        # Patch datetime.date in multiple modules where it's used
        self.date_patcher1 = patch('daily_rotating_file_handler.datetime.date', MockDate)
        self.date_patcher2 = patch('datetime.date', MockDate)
        # Also patch the datetime module itself
        self.datetime_patcher = patch('daily_rotating_file_handler.datetime', type('MockDateTime', (), {
            'date': MockDate,
            'datetime': datetime
        })())

        self.date_patcher1.start()
        self.date_patcher2.start()
        self.datetime_patcher.start()

        # Mock Telegram functions to avoid actual API calls
        self.telegram_patcher = patch('error.send_telegram_message', return_value={'ok': True})
        self.mock_send_message = self.telegram_patcher.start()

        # Track log directories created during test
        self.created_dirs = []

    def tearDown(self):
        """Clean up test environment while preserving directory for inspection"""
        print(f"\n=== Test Directory Preservation ===\nPath: {self.test_dir}\nContents: {os.listdir(self.test_dir)}\n========================")
        # Close all handlers for all loggers
        for logger_name in logging.Logger.manager.loggerDict:
            logger = logging.getLogger(logger_name)
            for handler in logger.handlers[:]:
                handler.flush()
                handler.close()
                logger.removeHandler(handler)
        

        os.chdir(self.original_cwd)
        # Keep temp dir for inspection by commenting out rmtree
        # shutil.rmtree(self.test_dir, ignore_errors=True)
        print(f"Test directory preserved at: {self.test_dir}")

        # Stop patchers
        self.date_patcher1.stop()
        self.date_patcher2.stop()
        self.datetime_patcher.stop()
        self.telegram_patcher.stop()
        
    def simulate_time_passage(self, minutes=0, days=0):
        """Helper to simulate time passage"""
        self.current_time += timedelta(minutes=minutes, days=days)
        MockDate._today_value = self.current_time.date()

    def set_mock_time(self, year, month, day, hour=0, minute=0):
        """Set the mock time to a specific datetime"""
        self.current_time = datetime(year, month, day, hour, minute)
        MockDate._today_value = self.current_time.date()
        
    def get_log_directories(self):
        """Get all log directories in the test environment"""
        return [d for d in os.listdir("logs") if os.path.isdir(os.path.join("logs", d))]
    
    def get_log_files(self, directory):
        """Get all log files in a specific directory"""
        dir_path = os.path.join("logs", directory)
        if not os.path.exists(dir_path):
            return []
        return [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
    
    def test_weekly_rotation_workflow(self):
        """Test the complete weekly rotation workflow with 5-minute 'days'"""
        print("\n=== Testing Weekly Rotation Workflow ===")

        # Start on Sunday at 00:00
        self.set_mock_time(2025, 8, 10)  # Sunday
        print(f"Mock date set to: {MockDate._today_value}")
        print(f"datetime.date.today() returns: {date.today()}")

        # Create logger
        logger = setup_logger("test", "test.log")
        
        # Simulate 7 days (35 minutes total, 5 minutes per "day")
        days_of_week = ["sunday", "monday", "tuesday", "wednesday", 
                        "thursday", "friday", "saturday"]
        
        for day_idx, day_name in enumerate(days_of_week):
            print(f"\n--- Simulating {day_name} (Day {day_idx+1}) ---")

            # Write log messages for this "day"
            for i in range(3):
                log(logger, "info", f"Log message {i+1} for {day_name}", send_on_telegram=False)
                time.sleep(0.1)  # Small delay to ensure different timestamps

            # Check current log directory
            current_dirs = self.get_log_directories()
            expected_dir = f"{day_name}_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"

            print(f"Expected directory: {expected_dir}")
            print(f"Actual directories: {current_dirs}")

            # Verify directory exists
            self.assertIn(expected_dir, current_dirs)
            self.created_dirs.append(expected_dir)

            # Verify log file exists in the directory
            log_files = self.get_log_files(expected_dir)
            self.assertIn("test.log", log_files)

            # Move to next "day" (1 day later)
            self.simulate_time_passage(days=1)
            print(f"After time passage, mock date is: {MockDate._today_value}")

            # Force handler to check for day change by writing another log
            log(logger, "info", f"End of {day_name} logs", send_on_telegram=False)
        
        # Now we're back to Sunday - check if old Sunday directory was deleted
        print("\n--- Back to Sunday - Checking Cleanup ---")
        
        # Write logs for the new Sunday
        for i in range(3):
            log(logger, "info", f"New Sunday log {i+1}", send_on_telegram=False)
            time.sleep(0.1)
        
        # Check directories
        current_dirs = self.get_log_directories()
        print(f"Current directories after rotation: {current_dirs}")
        
        # The old Sunday directory should be deleted
        old_sunday_dir = "sunday_10_08_2025"
        new_sunday_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        
        print(f"Old Sunday dir: {old_sunday_dir}")
        print(f"New Sunday dir: {new_sunday_dir}")
        
        # Verify old directory is gone
        self.assertNotIn(old_sunday_dir, current_dirs)
        
        # Verify new directory exists
        self.assertIn(new_sunday_dir, current_dirs)
        
        # Verify log file exists in new directory
        log_files = self.get_log_files(new_sunday_dir)
        self.assertIn("test.log", log_files)
        
        print("\n✅ Weekly rotation workflow test passed!")

    def test_simple_day_change(self):
        """Simple test to verify day change detection works"""
        print("\n=== Testing Simple Day Change ===")

        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday

        # Create a handler directly to test it
        with patch('daily_rotating_file_handler.datetime.date') as mock_date:
            mock_date.today.return_value = date(2025, 8, 10)
            mock_date.side_effect = lambda *args, **kw: date(*args, **kw)

            handler = DRFH("test.log")
            print(f"Handler created with current_date: {handler.current_date}")
            print(f"Handler current_day_dir: {handler.current_day_dir}")

            # Create a log record and emit it
            record = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Test message 1", args=(), exc_info=None
            )
            handler.emit(record)

            # Now change the date
            mock_date.today.return_value = date(2025, 8, 11)  # Monday
            print(f"Changed mock date to Monday")

            # Emit another record - this should trigger day change
            record2 = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Test message 2", args=(), exc_info=None
            )
            handler.emit(record2)

            print(f"After emit, handler current_date: {handler.current_date}")
            print(f"After emit, handler current_day_dir: {handler.current_day_dir}")

        print("\n✅ Simple day change test passed!")

    def test_multiple_loggers(self):
        """Test behavior with multiple loggers"""
        print("\n=== Testing Multiple Loggers ===")
        
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday
        
        # Create multiple loggers
        loggers = [
            setup_logger("main", "main.log"),
            setup_logger("docker", "docker_restarter.log"),
            setup_logger("emulator", "emulator.log")
        ]
        
        # Write logs for each logger
        for logger in loggers:
            log(logger, "info", f"Initial log from {logger.name}", send_on_telegram=False)
        
        # Check directories
        current_dirs = self.get_log_directories()
        expected_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(expected_dir, current_dirs)
        
        # Verify all log files exist
        log_files = self.get_log_files(expected_dir)
        self.assertIn("main.log", log_files)
        self.assertIn("docker_restarter.log", log_files)
        self.assertIn("emulator.log", log_files)
        
        # Move to next day
        self.simulate_time_passage(days=1)
        
        # Write more logs
        for logger in loggers:
            log(logger, "info", f"Next day log from {logger.name}", send_on_telegram=False)
        
        # Check new directory exists
        current_dirs = self.get_log_directories()
        expected_dir = f"monday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(expected_dir, current_dirs)
        
        print("\n✅ Multiple loggers test passed!")
    
    def test_cleanup_timing(self):
        """Test that cleanup only happens when appropriate"""
        print("\n=== Testing Cleanup Timing ===")
        
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday
        
        # Create logger
        logger = setup_logger("test", "test.log")
        
        # Write initial logs
        log(logger, "info", "Initial Sunday logs", send_on_telegram=False)
        
        # Verify directory exists
        current_dirs = self.get_log_directories()
        expected_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(expected_dir, current_dirs)
        
        # Move to Monday (1 day later)
        self.simulate_time_passage(days=1)
        log(logger, "info", "Monday logs", send_on_telegram=False)
        
        # Check that Sunday directory still exists (not yet 7 days old)
        current_dirs = self.get_log_directories()
        self.assertIn(expected_dir, current_dirs)
        
        # Simulate 6 more days
        for i in range(6):
            self.simulate_time_passage(days=1)
            log(logger, "info", f"Day {i+2} logs", send_on_telegram=False)
        
        # Now we're back to Sunday - check if old Sunday directory was deleted
        current_dirs = self.get_log_directories()
        self.assertNotIn(expected_dir, current_dirs)
        
        print("\n✅ Cleanup timing test passed!")
    
    def test_handler_day_detection(self):
        """Test that the handler correctly detects day changes"""
        print("\n=== Testing Handler Day Detection ===")
        
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday
        
        # Create a handler directly
        handler = DRFH("test.log")
        
        # Check initial directory
        expected_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertTrue(handler.current_day_dir.endswith(expected_dir))
        
        # Move to next day
        self.simulate_time_passage(days=1)
        
        # Create a log record and emit it
        record = logging.LogRecord(
            name="test", level=logging.INFO, pathname="", lineno=0,
            msg="Test message", args=(), exc_info=None
        )
        handler.emit(record)
        
        # Check that directory updated
        expected_dir = f"monday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertTrue(handler.current_day_dir.endswith(expected_dir))
        
        print("\n✅ Handler day detection test passed!")
    
    def test_idle_day_change(self):
        """Test behavior when no logs are written during day change"""
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)
        logger = setup_logger("test", "test.log")
        log(logger, "info", "Sunday log")
        
        # Move to Monday without writing logs
        self.simulate_time_passage(minutes=5)
        
        # Now write a log - should trigger cleanup and rotation
        log(logger, "info", "Monday log")
        
        # Verify Sunday directory still exists (not yet 7 days old)
        current_dirs = self.get_log_directories()
        self.assertIn("sunday_10_08_2025", current_dirs)
        
        print("\n✅ Idle day change test passed!")

    def test_timezone_handling(self):
        """Test behavior with different time zones"""
        # Start just before midnight in UTC
        self.set_mock_time(2025, 8, 10, 23, 59)
        logger = setup_logger("test", "test.log")
        
        # Move to next day in UTC
        self.simulate_time_passage(minutes=2)
        log(logger, "info", "After midnight UTC")
        
        # Should have created new directory
        current_dirs = self.get_log_directories()
        self.assertIn("monday_11_08_2025", current_dirs)
        
        print("\n✅ Time zone test passed!")
if __name__ == "__main__":
    unittest.main(verbosity=2)