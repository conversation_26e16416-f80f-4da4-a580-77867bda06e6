# numbers: list = [2, 3, 5, 4, 9, 0]

# target: int = 7

# i = 0
# while i < len(numbers):
#     for j in range(i+1, len(numbers)):
#         if numbers[j] + numbers[i] == target:
#             print((j, i))
#             break
#     else:
#         i += 1

# def two_sum(numbers, target):
#     i = 0
#     while i < len(numbers):
#         for j in range(i+1, len(numbers)):
#             if numbers[j] + numbers[i] == target:
#                 return((j, i))
#         i += 1


# def two_sum(numbers, target):
#     seen = {}
#     for i, num in enumerate(numbers):
#         complement = target - num
#         if complement in seen:
#             return (i, seen[complement])  # Returns (later index, earlier index)
#         seen[num] = i

# print(two_sum([2, 2, 3], 4))

def dna_s(dna):
    # return [dna.replace(i, {'A': 'T', 'G':'C', 'T':'A', 'C':'G'}.get(i)) for i in dna]
    return ''.join([{'A': 'T', 'G':'C', 'T':'A', 'C':'G'}.get(i) for i in dna])
    # complementary = ""
    # for i in dna.upper():
    #     complementary += {'A': 'T', 'G':'C', 'T':'A', 'C':'G'}.get(i)
    # return complementary

print(dna_s("ATGC"))


