#!/usr/bin/env python3
"""
Comprehensive Test Suite for Daily Rotating File Handler Logging System

This test suite combines features from multiple test files and provides comprehensive
coverage for:
- Daily log rotation
- Weekly cleanup of old logs
- Cross-platform compatibility
- Error handling
- Concurrent access
- Edge cases and boundary conditions
- Performance under load
- Multiple logger interactions
- Time zone handling
- File system edge cases
"""

import os
import shutil
import tempfile
import unittest
import threading
import time
import logging
import sys
import json
from datetime import datetime, timedelta, date
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path

# Import the logging system components
from error import setup_logger, log, DailyRotatingFileHandler
from daily_rotating_file_handler import DailyRotatingFileHandler as DRFH, DAY_NAMES, LOCK_FILE


class MockDate(date):
    """Mock date class that allows us to control what today() returns"""
    @classmethod
    def today(cls):
        return cls._today_value


class ComprehensiveLoggingTestSuite(unittest.TestCase):
    """Comprehensive test suite for the logging system"""
    
    def setUp(self):
        """Set up test environment with temporary directory and mocks"""
        # Create test directory
        self.workspace_path = "c:/Users/<USER>/OneDrive/Desktop/android_project/testing"
        test_temp_dir = os.path.join(self.workspace_path, "test_temp")
        os.makedirs(test_temp_dir, exist_ok=True)
        self.test_dir = tempfile.mkdtemp(dir=test_temp_dir)
        
        self.original_cwd = os.getcwd()
        os.chdir(self.test_dir)

        # Create required directories
        for directory in ["logs", "xml_log", "screenshots"]:
            os.makedirs(directory, exist_ok=True)

        # Set up time mocking
        self.current_time = datetime(2025, 8, 10, 12, 0, 0)  # Sunday midday
        MockDate._today_value = self.current_time.date()

        # Start patchers
        self._setup_patchers()
        
        # Clear any existing loggers
        self._clear_existing_loggers()
        
        # Track created directories for verification
        self.created_dirs = []
        self.test_results = {}

    def _setup_patchers(self):
        """Set up all necessary patchers for consistent testing"""
        # Date patchers
        self.date_patcher1 = patch('daily_rotating_file_handler.datetime.date', MockDate)
        self.date_patcher2 = patch('datetime.date', MockDate)
        
        # Mock the full datetime module in daily_rotating_file_handler
        mock_datetime = MagicMock()
        mock_datetime.date = MockDate
        mock_datetime.datetime = datetime
        self.datetime_patcher = patch('daily_rotating_file_handler.datetime', mock_datetime)

        # Telegram mocking to avoid API calls
        self.telegram_patcher = patch('error.send_telegram_message', return_value={'ok': True})
        self.telegram_photo_patcher = patch('error.send_telegram_photo', return_value={'ok': True})
        self.telegram_file_patcher = patch('error.send_telegram_file', return_value={'ok': True})

        # Start all patchers
        self.date_patcher1.start()
        self.date_patcher2.start() 
        self.datetime_patcher.start()
        self.mock_send_message = self.telegram_patcher.start()
        self.mock_send_photo = self.telegram_photo_patcher.start()
        self.mock_send_file = self.telegram_file_patcher.start()

    def _clear_existing_loggers(self):
        """Clear all existing loggers to avoid interference"""
        for logger_name in list(logging.Logger.manager.loggerDict.keys()):
            logger = logging.getLogger(logger_name)
            for handler in logger.handlers[:]:
                handler.flush()
                handler.close()
                logger.removeHandler(handler)
        
        # Clear the manager's dictionary
        logging.Logger.manager.loggerDict.clear()

    def tearDown(self):
        """Clean up test environment"""
        print(f"\n=== Test Directory: {self.test_dir} ===")
        
        # Close all handlers
        self._clear_existing_loggers()
        
        # Change back to original directory
        os.chdir(self.original_cwd)
        
        # Stop all patchers
        self.date_patcher1.stop()
        self.date_patcher2.stop()
        self.datetime_patcher.stop()
        self.telegram_patcher.stop()
        self.telegram_photo_patcher.stop()
        self.telegram_file_patcher.stop()
        
        # Keep test directory for inspection
        print(f"Test artifacts preserved at: {self.test_dir}")

    def simulate_time_passage(self, minutes=0, hours=0, days=0):
        """Helper to simulate time passage"""
        self.current_time += timedelta(minutes=minutes, hours=hours, days=days)
        MockDate._today_value = self.current_time.date()

    def set_mock_time(self, year, month, day, hour=0, minute=0, second=0):
        """Set the mock time to a specific datetime"""
        self.current_time = datetime(year, month, day, hour, minute, second)
        MockDate._today_value = self.current_time.date()

    def get_log_directories(self):
        """Get all log directories"""
        if not os.path.exists("logs"):
            return []
        return [d for d in os.listdir("logs") if os.path.isdir(os.path.join("logs", d))]

    def get_log_files(self, directory):
        """Get all log files in a specific directory"""
        dir_path = os.path.join("logs", directory)
        if not os.path.exists(dir_path):
            return []
        return [f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]

    def verify_log_content(self, directory, filename, expected_content_count=None):
        """Verify log file exists and optionally check content"""
        log_path = os.path.join("logs", directory, filename)
        self.assertTrue(os.path.exists(log_path), f"Log file {log_path} should exist")
        
        if expected_content_count:
            with open(log_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = [line for line in content.split('\n') if line.strip()]
                self.assertGreaterEqual(len(lines), expected_content_count,
                                      f"Expected at least {expected_content_count} lines in {log_path}")

    # ==================== CORE FUNCTIONALITY TESTS ====================

    def test_basic_daily_rotation(self):
        """Test basic daily rotation functionality"""
        print("\n=== Testing Basic Daily Rotation ===")
        
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday
        logger = setup_logger("test", "test.log")
        
        # Write initial logs
        log(logger, "info", "Sunday log message 1", send_on_telegram=False)
        log(logger, "info", "Sunday log message 2", send_on_telegram=False)
        
        # Verify Sunday directory created
        sunday_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(sunday_dir, self.get_log_directories())
        self.verify_log_content(sunday_dir, "test.log", 2)
        
        # Move to Monday
        self.simulate_time_passage(days=1)
        log(logger, "info", "Monday log message", send_on_telegram=False)
        
        # Verify Monday directory created
        monday_dir = f"monday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(monday_dir, self.get_log_directories())
        self.verify_log_content(monday_dir, "test.log", 1)
        
        # Verify Sunday directory still exists (not 7 days old yet)
        self.assertIn(sunday_dir, self.get_log_directories())
        
        print("✅ Basic daily rotation test passed!")

    def test_weekly_cleanup_cycle(self):
        """Test complete weekly cleanup cycle"""
        print("\n=== Testing Weekly Cleanup Cycle ===")
        
        # Start on Sunday
        self.set_mock_time(2025, 8, 10)  # Sunday
        logger = setup_logger("test", "test.log")
        
        # Log through a full week
        days_data = []
        for day_idx in range(7):
            day_name = DAY_NAMES[self.current_time.weekday()]
            expected_dir = f"{day_name}_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
            
            log(logger, "info", f"Log for {day_name}", send_on_telegram=False)
            
            # Verify directory created
            self.assertIn(expected_dir, self.get_log_directories())
            days_data.append((day_name, expected_dir))
            
            # Move to next day
            if day_idx < 6:  # Don't advance after Saturday
                self.simulate_time_passage(days=1)
        
        # Now we're at Saturday, advance to next Sunday (7 days after first Sunday)
        self.simulate_time_passage(days=1)
        
        # Log on new Sunday - should trigger cleanup of old Sunday
        log(logger, "info", "New Sunday log", send_on_telegram=False)
        
        current_dirs = self.get_log_directories()
        old_sunday_dir = days_data[0][1]  # First Sunday directory
        new_sunday_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        
        # Old Sunday should be gone, new Sunday should exist
        self.assertNotIn(old_sunday_dir, current_dirs)
        self.assertIn(new_sunday_dir, current_dirs)
        
        print("✅ Weekly cleanup cycle test passed!")

    def test_multiple_loggers_interaction(self):
        """Test multiple loggers working together"""
        print("\n=== Testing Multiple Loggers Interaction ===")
        
        self.set_mock_time(2025, 8, 10)  # Sunday
        
        # Create multiple loggers
        loggers = {
            "main": setup_logger("main", "main.log"),
            "docker": setup_logger("docker_restarter", "docker_restarter.log"),
            "emulator": setup_logger("emulator", "emulator.log"),
            "task": setup_logger("task_manager", "task_manager.log"),
            "device": setup_logger("device_manager", "device_manager.log")
        }
        
        # Write logs from all loggers
        for name, logger in loggers.items():
            log(logger, "info", f"Initial log from {name}", send_on_telegram=False)
            log(logger, "warning", f"Warning from {name}", send_on_telegram=False)
        
        # Verify all log files exist in same directory
        expected_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        log_files = self.get_log_files(expected_dir)
        
        expected_files = ["main.log", "docker_restarter.log", "emulator.log", "task_manager.log", "device_manager.log"]
        for expected_file in expected_files:
            self.assertIn(expected_file, log_files)
            self.verify_log_content(expected_dir, expected_file, 2)
        
        # Move to next day and verify rotation works for all
        self.simulate_time_passage(days=1)
        
        for name, logger in loggers.items():
            log(logger, "info", f"Monday log from {name}", send_on_telegram=False)
        
        monday_dir = f"monday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        monday_files = self.get_log_files(monday_dir)
        
        for expected_file in expected_files:
            self.assertIn(expected_file, monday_files)
        
        print("✅ Multiple loggers interaction test passed!")

    # ==================== EDGE CASES AND ERROR HANDLING ====================

    def test_midnight_boundary_rotation(self):
        """Test rotation exactly at midnight"""
        print("\n=== Testing Midnight Boundary Rotation ===")
        
        # Start just before midnight
        self.set_mock_time(2025, 8, 10, 23, 59, 50)
        logger = setup_logger("test", "test.log")
        
        # Log before midnight
        log(logger, "info", "Before midnight", send_on_telegram=False)
        
        sunday_dir = f"sunday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(sunday_dir, self.get_log_directories())
        
        # Cross midnight boundary
        self.simulate_time_passage(minutes=1)  # Now it's 00:00:50 on Monday
        
        # Log after midnight
        log(logger, "info", "After midnight", send_on_telegram=False)
        
        monday_dir = f"monday_{self.current_time.day:02d}_{self.current_time.month:02d}_{self.current_time.year}"
        self.assertIn(monday_dir, self.get_log_directories())
        
        # Verify both directories exist with correct content
        self.verify_log_content(sunday_dir, "test.log", 1)
        self.verify_log_content(monday_dir, "test.log", 1)
        
        print("✅ Midnight boundary rotation test passed!")

    def test_concurrent_access(self):
        """Test concurrent access to logging system"""
        print("\n=== Testing Concurrent Access ===")
        
        self.set_mock_time(2025, 8, 10)
        
        # Create shared logger
        logger = setup_logger("concurrent", "concurrent.log")
        
        # Track results from threads
        results = []
        errors = []
        
        def worker_thread(worker_id, num_logs=5):
            """Worker thread that writes logs"""
            try:
                for i in range(num_logs):
                    log(logger, "info", f"Worker {worker_id} log {i}", send_on_telegram=False)
                    time.sleep(0.01)  # Small delay
                results.append(f"Worker {worker_id} completed")
            except Exception as e:
                errors.append(f"Worker {worker_id} error: {e}")
        
        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker_thread, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join(timeout=10)
        
        # Verify no errors occurred
        self.assertEqual(len(errors), 0, f"Concurrent access errors: {errors}")
        self.assertEqual(len(results), 5, "Not all worker threads completed")
        
        # Verify log file exists and has content
        expected_dir = f"sunday_10_08_2025"
        self.verify_log_content(expected_dir, "concurrent.log", 20)  # 5 workers * 5 logs each
        
        print("✅ Concurrent access test passed!")

    def test_telegram_integration(self):
        """Test telegram integration with different log levels"""
        print("\n=== Testing Telegram Integration ===")
        
        self.set_mock_time(2025, 8, 10)
        logger = setup_logger("telegram_test", "telegram_test.log")
        
        # Test different log levels
        log(logger, "info", "Info message", send_on_telegram=True)
        log(logger, "warning", "Warning message", send_on_telegram=True)
        log(logger, "error", "Error message", send_on_telegram=True)
        
        # Test with screenshot
        fake_screenshot = b"fake_image_data"
        log(logger, "error", "Error with screenshot", send_on_telegram=True, screenshot=fake_screenshot)
        
        # Test with file
        test_data = {"test": "data", "timestamp": "2025-08-10"}
        log(logger, "error", "Error with file", send_on_telegram=True, file=test_data)
        
        # Verify telegram calls were made
        self.assertTrue(self.mock_send_message.called)
        self.assertTrue(self.mock_send_photo.called)
        self.assertTrue(self.mock_send_file.called)
        
        # Verify log file still created locally
        expected_dir = f"sunday_10_08_2025"
        self.verify_log_content(expected_dir, "telegram_test.log", 5)
        
        print("✅ Telegram integration test passed!")

    def test_high_volume_logging(self):
        """Test system performance under high log volume"""
        print("\n=== Testing High Volume Logging ===")
        
        self.set_mock_time(2025, 8, 10)
        logger = setup_logger("high_volume", "high_volume.log")
        
        start_time = time.time()
        
        # Generate high volume of logs
        for i in range(500):  # Reduced for faster testing
            log(logger, "info", f"High volume log message {i}", send_on_telegram=False)
            
            # Change day every 100 logs to test rotation under load
            if i % 100 == 0 and i > 0:
                self.simulate_time_passage(days=1)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Generated 500 logs in {duration:.2f} seconds")
        
        # Verify logs were written
        current_dirs = self.get_log_directories()
        self.assertGreater(len(current_dirs), 0)
        
        # Check that at least some logs were written to files
        total_log_files = 0
        for directory in current_dirs:
            log_files = self.get_log_files(directory)
            if "high_volume.log" in log_files:
                total_log_files += 1
        
        self.assertGreater(total_log_files, 0)
        
        print("✅ High volume logging test passed!")

    def test_comprehensive_workflow(self):
        """Comprehensive workflow test combining multiple scenarios"""
        print("\n=== Running Comprehensive Workflow Test ===")
        
        # This test combines multiple scenarios in a realistic workflow
        
        # Phase 1: Application startup and initial logging
        self.set_mock_time(2025, 8, 10, 9, 0, 0)  # Sunday morning
        
        loggers = {
            "main": setup_logger("main", "main.log"),
            "auth": setup_logger("auth", "auth.log"),
            "api": setup_logger("api", "api.log")
        }
        
        # Initial startup logs
        for name, logger in loggers.items():
            log(logger, "info", f"{name} service starting up", send_on_telegram=False)
        
        # Phase 2: Normal operation throughout the day
        for hour in range(10, 18):  # 10 AM to 6 PM
            self.set_mock_time(2025, 8, 10, hour, 0, 0)
            
            for name, logger in loggers.items():
                log(logger, "info", f"{name} hourly status at {hour}:00", send_on_telegram=False)
                
                # Occasional warnings and errors
                if hour % 3 == 0:
                    log(logger, "warning", f"{name} minor issue at {hour}:00", send_on_telegram=True)
                if hour % 5 == 0:
                    log(logger, "error", f"{name} error handled at {hour}:00", send_on_telegram=True)
        
        # Phase 3: Run for a full week with day transitions
        for day_offset in range(1, 8):
            self.simulate_time_passage(days=1)
            
            day_name = DAY_NAMES[self.current_time.weekday()]
            
            for name, logger in loggers.items():
                log(logger, "info", f"{name} daily startup on {day_name}", send_on_telegram=False)
        
        # Phase 4: Continue for another week to test cleanup
        for day_offset in range(8, 15):
            self.simulate_time_passage(days=1)
            
            day_name = DAY_NAMES[self.current_time.weekday()]
            
            # Reduced logging for second week
            log(loggers["main"], "info", f"Week 2 {day_name} operations", send_on_telegram=False)
        
        # Phase 5: Verify final state
        final_dirs = self.get_log_directories()
        
        # Should have cleaned up first week's directories
        self.assertLessEqual(len(final_dirs), 8)
        
        # Verify that all logger files exist in recent directories
        for directory in final_dirs:
            log_files = self.get_log_files(directory)
            if any(f.endswith('.log') for f in log_files):
                # This directory has log files, verify structure
                self.assertTrue(os.path.exists(os.path.join("logs", directory)))
        
        print("✅ Comprehensive workflow test passed!")


def run_comprehensive_tests():
    """Run all comprehensive tests with detailed reporting"""
    
    print("="*80)
    print("COMPREHENSIVE LOGGING SYSTEM TEST SUITE")
    print("="*80)
    print("This test suite validates all aspects of the logging system:")
    print("- Daily rotation functionality")
    print("- Weekly cleanup cycles") 
    print("- Multiple logger coordination")
    print("- Edge case handling")
    print("- Error recovery")
    print("- Performance under load")
    print("- Telegram integration")
    print("- Real-world scenarios")
    print("="*80)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add all test methods
    test_class = ComprehensiveLoggingTestSuite
    
    # Core functionality tests
    suite.addTest(test_class('test_basic_daily_rotation'))
    suite.addTest(test_class('test_weekly_cleanup_cycle'))
    suite.addTest(test_class('test_multiple_loggers_interaction'))
    
    # Edge cases and error handling
    suite.addTest(test_class('test_midnight_boundary_rotation'))
    suite.addTest(test_class('test_concurrent_access'))
    
    # Telegram integration
    suite.addTest(test_class('test_telegram_integration'))
    
    # Performance tests  
    suite.addTest(test_class('test_high_volume_logging'))
    
    # Comprehensive workflow
    suite.addTest(test_class('test_comprehensive_workflow'))
    
    # Run the tests
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n🎉 ALL TESTS PASSED! The logging system is ready for production use.")
        print("\nKey Features Validated:")
        print("✅ Daily log rotation works correctly")
        print("✅ Weekly cleanup maintains proper log retention")
        print("✅ Multiple loggers coordinate seamlessly")
        print("✅ Midnight transitions handled properly")
        print("✅ Concurrent access is thread-safe")
        print("✅ Telegram integration functions correctly")
        print("✅ High-volume logging performs well")
        print("✅ Complex workflows execute successfully")
    else:
        print(f"\n❌ Some tests failed. Please review and fix the issues.")
    
    print("="*80)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1) 