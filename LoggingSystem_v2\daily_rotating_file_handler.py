import logging
import os
import datetime
import shutil
import time
import threading

# Handle fcntl for cross-platform compatibility
try:
    import fcntl  # For file locking on Unix-like systems
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False  # Windows doesn\'t have fcntl

DAY_NAMES = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
LOCK_FILE = "logs/.cleanup_lock"  # Lock file for cleanup synchronization

class DailyRotatingFileHandler(logging.FileHandler):
    def __init__(self, filename, mode='a', encoding=None, delay=False):
        self.baseFilename = filename
        # Store the original base directory to avoid nested directory issues
        self.original_base_dir = os.path.dirname(filename) if os.path.dirname(filename) else 'logs'
        self.original_filename = os.path.basename(filename)
        self.current_date = datetime.date.today()
        self.current_day_dir = self._get_day_log_directory(self.current_date)
        self._ensure_log_directory_exists()
        self._cleanup_old_logs_for_current_day()

        # Initialize the FileHandler with the current day\'s log file path
        # The actual file path will be updated in emit if the day changes
        log_file_path = os.path.join(self.current_day_dir, self.original_filename)
        super().__init__(log_file_path, mode, encoding, delay)

    def _get_day_log_directory(self, date_obj):
        """Return log directory path in \'logs/<dayname>_<dd_mm_yyyy>\' format."""
        day_name = DAY_NAMES[date_obj.weekday()]
        # Use the original base directory to avoid nested directory issues
        folder_name = f"{day_name}_{date_obj.day:02d}_{date_obj.month:02d}_{date_obj.year}"
        return os.path.join(self.original_base_dir, folder_name)

    def _ensure_log_directory_exists(self):
        """Ensures the current day\'s log directory exists."""
        os.makedirs(self.current_day_dir, exist_ok=True)

    def _parse_folder_date_and_day(self, folder_name):
        """
        Parse folder name to extract day name and date.
        Returns (day_name, datetime_obj) or (None, None) if parsing fails.
        """
        try:
            parts = folder_name.split("_")
            if len(parts) != 4:
                return None, None
            
            day_name, day, month, year = parts
            if day_name.lower() not in DAY_NAMES:
                return None, None
                
            folder_date = datetime.datetime(int(year), int(month), int(day)).date()
            return day_name.lower(), folder_date
        except (ValueError, IndexError):
            return None, None

    def _should_cleanup_folder(self, folder_name, current_date):
        """
        Determine if a folder should be cleaned up based on weekly cycle logic.
        A folder should be cleaned up if:
        1. It\'s the same day of the week as today
        2. It\'s from a previous week (more than 7 days old)
        """
        day_name, folder_date = self._parse_folder_date_and_day(folder_name)
        if not day_name or not folder_date:
            return False
        
        current_day_name = DAY_NAMES[current_date.weekday()]
        
        # Only cleanup folders from the same day of the week
        if day_name != current_day_name:
            return False
        
        # Check if it\'s from a previous week (more than 7 days old)
        days_diff = (current_date - folder_date).days
        return days_diff >= 7

    def _cleanup_old_logs_for_current_day(self):
        """
        Remove log directories for the current day of the week that are older than 7 days.
        Uses a lock file to avoid parallel cleanup conflicts.
        """
        # Use the original base directory for cleanup
        root_log_dir = self.original_base_dir
        if not os.path.exists(root_log_dir):
            return

        # Cross-platform file locking
        lock_acquired = False
        lockfile = None
        
        try:
            lockfile = open(LOCK_FILE, "w")
            
            if HAS_FCNTL:
                try:
                    fcntl.flock(lockfile, fcntl.LOCK_EX | fcntl.LOCK_NB)
                    lock_acquired = True
                except BlockingIOError:
                    # Another process is already cleaning up
                    return
            else:
                # On Windows, use a simpler approach: check if lock file was recently created
                try:
                    stat = os.stat(LOCK_FILE)
                    if datetime.datetime.now().timestamp() - stat.st_mtime < 60:
                        return  # Recently created, assume another process is cleaning
                except OSError:
                    pass
                lock_acquired = True

            if not lock_acquired:
                return

            current_date = self.current_date
            current_day_name = DAY_NAMES[current_date.weekday()]
            
            # print(f"[CLEANUP] Starting cleanup for {current_day_name} logs...") # Use proper logging later

            for folder in os.listdir(root_log_dir):
                folder_path = os.path.join(root_log_dir, folder)
                if not os.path.isdir(folder_path):
                    continue
                    
                if self._should_cleanup_folder(folder, current_date):
                    try:
                        shutil.rmtree(folder_path)
                        # print(f"[CLEANUP] Removed old {current_day_name} log folder: {folder_path}") # Use proper logging later
                    except Exception as e:
                        # print(f"[CLEANUP WARNING] Could not remove {folder_path}: {e}") # Use proper logging later
                        pass # Suppress for now, proper logging will be added in error.py

            # print(f"[CLEANUP] Cleanup completed for {current_day_name} logs") # Use proper logging later

        except Exception as e:
            # print(f"[CLEANUP ERROR] Cleanup failed: {e}") # Use proper logging later
            pass # Suppress for now, proper logging will be added in error.py
        finally:
            if lockfile:
                try:
                    if HAS_FCNTL and lock_acquired:
                        fcntl.flock(lockfile, fcntl.LOCK_UN)
                    lockfile.close()
                except Exception as e:
                    # print(f"[CLEANUP WARNING] Could not release lock: {e}") # Use proper logging later
                    pass # Suppress for now, proper logging will be added in error.py

    def emit(self, record):
        """Emit a record. Rotate log file if day has changed."""
        new_date = datetime.date.today()
        if new_date != self.current_date:
            # Day has changed, rotate the log file
            self.current_date = new_date
            self.current_day_dir = self._get_day_log_directory(self.current_date)
            self._ensure_log_directory_exists()
            self._cleanup_old_logs_for_current_day()
            
            # Close the current stream and open a new one for the new day\'s file
            self.stream.close()
            self.baseFilename = os.path.join(self.current_day_dir, self.original_filename)
            self.stream = self._open()
        
        super().emit(record)


# Ensure required directories exist for the lock file
if not os.path.exists("logs"):
    os.makedirs("logs")