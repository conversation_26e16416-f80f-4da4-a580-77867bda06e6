# def stray(arr):
#     for i in range(len(arr)):
#         if arr[i] == arr[i+1]:
#             continue
#         try:
#             return arr[i] if (arr[i] != arr[i-1]) else arr[i+1]
#         except IndexError: 
#             return arr[i] if (arr[i] != arr[i+2]) else arr[i+1]

# print(stray([17, 17, 3, 17, 17, 17, 17]))

def stray(arr):
    for i in arr:
        if arr.count(i) > 1:
            continue
        return(i)

print(stray([17, 17, 3, 17, 17, 17, 17]))