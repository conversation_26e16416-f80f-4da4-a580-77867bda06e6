import io
import json
import logging
import os
import threading
from datetime import datetime, timedelta
from pathlib import Path
import shutil
import sys
import requests
from dotenv import load_dotenv

# Import the new DailyRotatingFileHandler
from daily_rotating_file_handler import DailyRotatingFileHandler

# Handle fcntl for cross-platform compatibility
try:
    import fcntl  # For file locking on Unix-like systems
    HAS_FCNTL = True
except ImportError:
    HAS_FCNTL = False  # Windows doesn\'t have fcntl

load_dotenv()
TELEGRAM_DEFAULT_CHANNEL_ID = os.getenv("TG_CHANNEL_ID")
SERVER_ID = os.getenv("SERVER_ID")
TG_BOT_TOKEN = os.getenv("TG_BOT_TOKEN")
ERROR_CONFIG = {
    "telegram levels": ["error", "warning"],
}
logger_tg_channels = {}

# Configuration (moved to daily_rotating_file_handler for consistency)
# DAY_NAMES = [\'monday\', \'tuesday\', \'wednesday\', \'thursday\', \'friday\', \'saturday\', \'sunday\']
# LOCK_FILE = "logs/.cleanup_lock"  # Lock file for cleanup synchronization

# These functions are now handled within DailyRotatingFileHandler
# def get_current_day_name():
#     """Return the current weekday name in lowercase."""
#     return DAY_NAMES[datetime.now().weekday()]

# def get_day_log_directory(day_name=None, date_obj=None):
#     """Return log directory path in \'logs/<dayname>_<dd_mm_yyyy>\' format."""
#     if day_name is None:
#         day_name = get_current_day_name()
#     if date_obj is None:
#         date_obj = datetime.now()
#     folder_name = f"{day_name}_{date_obj.day}_{date_obj.month}_{date_obj.year}"
#     return os.path.join("logs", folder_name)

# def parse_folder_date_and_day(folder_name):
#     """
#     Parse folder name to extract day name and date.
#     Returns (day_name, datetime_obj) or (None, None) if parsing fails.
#     """
#     try:
#         parts = folder_name.split("_")
#         if len(parts) != 4:
#             return None, None
        
#         day_name, day, month, year = parts
#         if day_name.lower() not in DAY_NAMES:
#             return None, None
            
#         folder_date = datetime(int(year), int(month), int(day))
#         return day_name.lower(), folder_date
#     except (ValueError, IndexError):
#         return None, None

# def should_cleanup_folder(folder_name, current_date):
#     """
#     Determine if a folder should be cleaned up based on weekly cycle logic.
#     A folder should be cleaned up if:
#     1. It\'s the same day of the week as today
#     2. It\'s from a previous week (more than 7 days old)
#     """
#     day_name, folder_date = parse_folder_date_and_day(folder_name)
#     if not day_name or not folder_date:
#         return False
    
#     current_day_name = get_current_day_name()
    
#     # Only cleanup folders from the same day of the week
#     if day_name != current_day_name:
#         return False
    
#     # Check if it\'s from a previous week (more than 7 days old)
#     days_diff = (current_date - folder_date).days
#     return days_diff >= 7

# def cleanup_old_logs(base_logs_dir="logs"):
#     """
#     Remove log directories following weekly cycle logic:
#     - Only cleanup folders from the same day of the week as today
#     - Only cleanup if they\'re from previous weeks (7+ days old)
#     Uses a lock file to avoid parallel cleanup conflicts.
#     """
#     os.makedirs(base_logs_dir, exist_ok=True)

#     # Cross-platform file locking
#     lock_acquired = False
#     lockfile = None
    
#     try:
#         lockfile = open(LOCK_FILE, "w")
        
#         if HAS_FCNTL:
#             try:
#                 fcntl.flock(lockfile, fcntl.LOCK_EX | fcntl.LOCK_NB)
#                 lock_acquired = True
#             except BlockingIOError:
#                 # Another process is already cleaning up
#                 return
#         else:
#             # On Windows, we\'ll use a simpler approach
#             # Check if lock file was recently created (within last minute)
#             try:
#                 stat = os.stat(LOCK_FILE)
#                 if datetime.now().timestamp() - stat.st_mtime < 60:
#                     return  # Recently created, assume another process is cleaning
#             except OSError:
#                 pass
#             lock_acquired = True

#         if not lock_acquired:
#             return

#         now = datetime.now()
#         current_day_name = get_current_day_name()
        
#         print(f"[CLEANUP] Starting cleanup for {current_day_name} logs...")

#         for folder in os.listdir(base_logs_dir):
#             folder_path = os.path.join(base_logs_dir, folder)
#             if not os.path.isdir(folder_path):
#                 continue
                
#             if should_cleanup_folder(folder, now):
#                 try:
#                     shutil.rmtree(folder_path)
#                     print(f"[CLEANUP] Removed old {current_day_name} log folder: {folder_path}")
#                 except Exception as e:
#                     print(f"[CLEANUP WARNING] Could not remove {folder_path}: {e}")

#         print(f"[CLEANUP] Cleanup completed for {current_day_name} logs")

#     except Exception as e:
#         print(f"[CLEANUP ERROR] Cleanup failed: {e}")
#     finally:
#         if lockfile:
#             try:
#                 if HAS_FCNTL and lock_acquired:
#                     fcntl.flock(lockfile, fcntl.LOCK_UN)
#                 lockfile.close()
#             except Exception as e:
#                 print(f"[CLEANUP WARNING] Could not release lock: {e}")


# def get_day_based_log_path(original_log_file):
#     """Generate a full log path inside the date-based day directory."""
#     filename = os.path.basename(original_log_file)
#     day_directory = get_day_log_directory()
#     os.makedirs(day_directory, exist_ok=True)

#     # Cleanup old logs for the current day of the week before creating today\'s log file
#     cleanup_old_logs(os.path.dirname(day_directory))

#     return os.path.join(day_directory, filename)

def telegram_wrapper(func):
    """Run the function in a background thread without blocking."""
    def wrapper(*args, **kwargs):
        thread = threading.Thread(target=func, args=args, kwargs=kwargs)
        thread.daemon = True
        thread.start()
    return wrapper


@telegram_wrapper
def send_telegram_message(message, channel=TELEGRAM_DEFAULT_CHANNEL_ID):
    url = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendMessage"
    payload = {"chat_id": channel, "text": message[:4000]}
    response = requests.post(url, json=payload)
    if response.status_code > 299:
        raise Exception(response.text[:100])
    return response.json()


@telegram_wrapper
def send_telegram_photo(message, image_bytes, channel=TELEGRAM_DEFAULT_CHANNEL_ID):
    url_photo = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendPhoto"
    with io.BytesIO(image_bytes) as image_file:
        payload_photo = {"chat_id": channel, "caption": message[:4000]}
        files = {"photo": image_file}
        response_photo = requests.post(url_photo, data=payload_photo, files=files)
        if response.status_code > 299:
            raise Exception(response_photo.text[:100])
    return response.json()


@telegram_wrapper
def send_telegram_file(message, file_data, filename="data.json", channel=TELEGRAM_DEFAULT_CHANNEL_ID):
    url_document = f"https://api.telegram.org/bot{TG_BOT_TOKEN}/sendDocument"

    if isinstance(file_data, dict):
        file_bytes = json.dumps(file_data, ensure_ascii=False, indent=4).encode("utf-8")
    elif isinstance(file_data, str):
        file_bytes = file_data.encode("utf-8")
    elif isinstance(file_data, bytes):
        file_bytes = file_data
    else:
        raise TypeError("file_data must be dict, str, or bytes")

    with io.BytesIO(file_bytes) as file_obj:
        payload = {"chat_id": channel, "caption": message[:1000]}
        files = {"document": (filename, file_obj, "application/json")}
        response = requests.post(url_document, data=payload, files=files)
        if response.status_code > 299:
            raise Exception(response.text[:100])
    return response.json()


def setup_logger(name, log_file, tg_channel=TELEGRAM_DEFAULT_CHANNEL_ID, level=logging.INFO):
    logger = logging.getLogger(name)
    logger.setLevel(level)
    logger_tg_channels[name] = tg_channel

    if not logger.handlers:
        # Use DailyRotatingFileHandler for automatic daily rotation and cleanup
        file_handler = DailyRotatingFileHandler(log_file, encoding="utf-8")
        file_handler.setLevel(level)
        stream = logging.StreamHandler()

        class CustomFormatter(logging.Formatter):
            def format(self, record):
                if not hasattr(record, "context"):
                    record.context = ""
                return super().format(record)

        formatter = CustomFormatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s - %(context)s"
        )

        file_handler.setFormatter(formatter)
        stream.setFormatter(formatter)

        logger.addHandler(file_handler)
        logger.addHandler(stream)

    return logger


# Ensure required directories exist
for base_dir in ["logs", "xml_log", "screenshots"]:
    os.makedirs(base_dir, exist_ok=True)


def log(logger: logging.Logger, level, text, send_on_telegram=True,
        screenshot: bytes | None = None, exc_info: bool = True, **context):

    server_prefix = f"[SERVER:{SERVER_ID}] " if SERVER_ID else ""
    prefixed_text = f"{server_prefix}{text}"

    if (send_on_telegram and level.lower() in ERROR_CONFIG["telegram levels"]) or screenshot or "file" in context:
        try:
            t = f"{server_prefix}{logger.name} :\n{level}\n{text}\n"
            file = None
            if context:
                file = context.pop("file", None)
                t += f"{json.dumps(context, ensure_ascii=False, indent=4)}"

            if file:
                try:
                    filename = context.get("filename", "data.json")
                    send_telegram_file(
                        t, file, filename=filename,
                        channel=logger_tg_channels[logger.name]
                    )
                except Exception as e:
                    send_telegram_message(
                        t + "\nfile couldn\'t be sent\n" + str(e),
                        channel=logger_tg_channels[logger.name],
                    )
            elif screenshot is not None:
                try:
                    send_telegram_photo(
                        t, screenshot, channel=logger_tg_channels[logger.name]
                    )
                except Exception as e:
                    send_telegram_message(
                        t + "\nscreenshot couldn\'t be sent\n" + str(e),
                        channel=logger_tg_channels[logger.name],
                    )
            else:
                send_telegram_message(t, channel=logger_tg_channels[logger.name])
        except Exception as e:
            log(logger, "info", "couldn\'t send on telegram\n" + str(e), False)

    if hasattr(logger, level.lower()):
        method = getattr(logger, level.lower())
    else:
        method = logger.info

    if screenshot:
        context["with_photo"] = True
    if "file" in context:
        context["with_file"] = True

    method(prefixed_text + "\n", extra={"context": context}, exc_info=exc_info)


