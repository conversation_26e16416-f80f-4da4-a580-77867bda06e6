# strarr = ["tree", "foling", "trashy", "blue", "abcdef", "uvwxyz"]
# k = 2
# len_dict = {}
# # for i in range(len(strarr) - 1):
# #     for j in range(i, i+k):
# #         len_list.append((strarr[i] + strarr[j+1]))
# # print(len_list)
# for i in range(len(strarr) - k + 1):
#     concatenated = ''.join(strarr[j] for j in range (i, i+k) )
#     len_dict[len(concatenated)] = concatenated if (len(concatenated) not in len_dict) else len_dict[len(concatenated)]

# print(len_dict[max(len_dict.keys())])

def longest_consec(strarr, k):
    n = len(strarr)
    if (n == 0) or (k > n) or (k <= 0):
        return ""
    len_conc_dict = {}
    for i in range(len(strarr) - k + 1):
        concatenated = ''.join(strarr[j] for j in range (i, i+k) )
        len_conc_dict[len(concatenated)] = concatenated if (len(concatenated) not in len_conc_dict) else len_conc_dict[len(concatenated)]
    return (len_conc_dict[max(len_conc_dict.keys())])

print((longest_consec(["itvayloxrp","wkppqsztdkmvcuwvereiupccauycnjutlv","vweqilsfytihvrzlaodfixoyxvyuyvgpck"], 2)) == "wkppqsztdkmvcuwvereiupccauycnjutlvvweqilsfytihvrzlaodfixoyxvyuyvgpck")

print(longest_consec(["zone", "abigail", "theta", "form", "libe", "zas", "theta", "abigail"], 2) == "abigailtheta")

# def longest_consec(s, k):
#     return max(["".join(s[i:i+k]) for i in range(len(s)-k+1)], key=len) if s and 0 < k <= len(s) else ""