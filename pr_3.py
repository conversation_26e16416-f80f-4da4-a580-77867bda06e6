# 4824 -> 135440716410000

def find_m(n):
    # m = n^3 + (n-1)^3 + ... + 1^3
    result = 0
    for i in range(n, 0, -1):
        if i == 1:
            return result + 1
            break
        result += i**3

# print(f"m= {find_m(4824)}")

def find_n(m):
    result = 0
    i = 1
    while i < m:
        result += i**3
        if (result == m):
            return i
        elif (result > m):
            return -1
        i += 1

print(find_n(26825883955641))
