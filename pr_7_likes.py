def likes(names):
    match names:
        case []:
            return "no one likes this"
        case [a]:
            return f"{a} likes this"
        case [a, b]:
            return f"{a} and {b} like this"
        case [a, b, c]:
            return f"{a}, {b} and {c} like this"
        case [a, b, *rest]:
            return f"{a}, {b} and {len(rest)} others like this"


print(likes([]))                        # "no one likes this"
print(likes(["<PERSON>"]))                 # "<PERSON> likes this"
print(likes(["<PERSON>", "<PERSON>"]))         # "<PERSON> and <PERSON> like this"
print(likes(["<PERSON>", "<PERSON>", "<PERSON>"]))   # "<PERSON>, <PERSON> and <PERSON> like this"
print(likes(["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"]))  # "<PERSON>, <PERSON> and 2 others like this"
print(likes(["A", "B", "C", "D", "E"])) # "A, B and 3 others like this"