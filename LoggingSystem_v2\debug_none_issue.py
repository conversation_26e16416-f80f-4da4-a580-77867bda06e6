import os
import tempfile
import shutil
from unittest.mock import patch
import logging

# Import the logging system
from error import setup_logger, log

def debug_none_issue():
    """Debug the NoneType: None issue"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    os.chdir(test_dir)
    
    try:
        # Create logs directory
        os.makedirs("logs", exist_ok=True)
        
        print("=== Testing log function output ===")
        
        # Mock telegram function
        with patch('error.send_telegram_message', return_value={'ok': True}) as mock_telegram:
            # Create logger
            logger = setup_logger("test", "test.log")
            
            print("1. Testing with send_on_telegram=False:")
            result = log(logger, "info", "Test message 1", send_on_telegram=False)
            print(f"Return value: {result}")
            print(f"Type: {type(result)}")
            
            print("\n2. Testing with send_on_telegram=True (default):")
            result = log(logger, "info", "Test message 2")
            print(f"Return value: {result}")
            print(f"Type: {type(result)}")
            
            print(f"\n3. Telegram mock called: {mock_telegram.called}")
            print(f"Telegram mock call count: {mock_telegram.call_count}")
            
            # Close handlers
            for handler in logger.handlers[:]:
                handler.close()
                logger.removeHandler(handler)
            
    finally:
        # Clean up
        os.chdir(original_cwd)
        shutil.rmtree(test_dir, ignore_errors=True)

if __name__ == "__main__":
    debug_none_issue()
