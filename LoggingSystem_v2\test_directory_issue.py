import os
import tempfile
import shutil
from unittest.mock import patch
from datetime import date as real_date, datetime
import logging

# Import the handler directly
from daily_rotating_file_handler import DailyRotatingFileHandler

# Create a mock date class with a today method we can patch
class MockDate(real_date):
    @classmethod
    def today(cls):
        return cls._today_value

def test_directory_path_issue():
    """Test to reproduce and understand the directory path issue"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    os.chdir(test_dir)
    
    try:
        # Create logs directory
        os.makedirs("logs", exist_ok=True)
        
        print(f"Testing in directory: {test_dir}")
        
        # Path to patch: the datetime module in daily_rotating_file_handler
        patch_path = 'daily_rotating_file_handler.datetime.date'
        
        with patch(patch_path, MockDate):
            # Start on Sunday
            MockDate._today_value = datetime(2025, 8, 10).date()  # Sunday
            print(f"Starting on Sunday: {MockDate._today_value}")
            
            # Create handler
            handler = DailyRotatingFileHandler("test.log")
            print(f"Handler created with:")
            print(f"  baseFilename: {handler.baseFilename}")
            print(f"  current_day_dir: {handler.current_day_dir}")
            print(f"  current_date: {handler.current_date}")
            
            # Check what directories exist
            print(f"Directories after Sunday handler creation: {os.listdir('logs') if os.path.exists('logs') else 'No logs dir'}")
            
            # Create a log record and emit it
            record = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Sunday log message", args=(), exc_info=None
            )
            handler.emit(record)
            print(f"After Sunday emit - directories: {os.listdir('logs') if os.path.exists('logs') else 'No logs dir'}")
            
            # Simulate a full week to test cleanup
            days_and_dates = [
                ('monday', datetime(2025, 8, 11).date()),
                ('tuesday', datetime(2025, 8, 12).date()),
                ('wednesday', datetime(2025, 8, 13).date()),
                ('thursday', datetime(2025, 8, 14).date()),
                ('friday', datetime(2025, 8, 15).date()),
                ('saturday', datetime(2025, 8, 16).date()),
            ]

            for day_name, day_date in days_and_dates:
                MockDate._today_value = day_date
                print(f"\n--- {day_name.capitalize()}: {MockDate._today_value} ---")

                record = logging.LogRecord(
                    name="test", level=logging.INFO, pathname="", lineno=0,
                    msg=f"{day_name.capitalize()} log", args=(), exc_info=None
                )
                handler.emit(record)
                print(f"Directories: {os.listdir('logs')}")

            # Now test second Sunday (exactly 7 days later)
            MockDate._today_value = datetime(2025, 8, 17).date()  # Second Sunday
            print(f"\n=== Second Sunday: {MockDate._today_value} ===")
            print(f"Days difference from first Sunday: {(MockDate._today_value - datetime(2025, 8, 10).date()).days}")

            # Before emitting, check what should be cleaned up
            print(f"Before second Sunday emit - directories: {os.listdir('logs')}")

            # Debug the cleanup logic
            for folder in os.listdir('logs'):
                if os.path.isdir(os.path.join('logs', folder)):
                    should_cleanup = handler._should_cleanup_folder(folder, MockDate._today_value)
                    day_name, folder_date = handler._parse_folder_date_and_day(folder)
                    if day_name and folder_date:
                        days_diff = (MockDate._today_value - folder_date).days
                        print(f"Folder: {folder}")
                        print(f"  Parsed day: {day_name}, date: {folder_date}")
                        print(f"  Days difference: {days_diff}")
                        print(f"  Should cleanup: {should_cleanup}")

            # Emit second Sunday log
            record2 = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Second Sunday log", args=(), exc_info=None
            )
            handler.emit(record2)
            
            print(f"After second Sunday emit:")
            print(f"  Handler baseFilename: {handler.baseFilename}")
            print(f"  Handler current_day_dir: {handler.current_day_dir}")
            print(f"  Handler current_date: {handler.current_date}")

            # Check final directory structure
            print(f"Final directories: {os.listdir('logs')}")

            # Check if old Sunday folder was deleted
            old_sunday = 'sunday_10_08_2025'
            new_sunday = 'sunday_17_08_2025'

            if old_sunday in os.listdir('logs'):
                print(f"❌ OLD SUNDAY FOLDER STILL EXISTS: {old_sunday}")
            else:
                print(f"✅ Old Sunday folder was deleted: {old_sunday}")

            if new_sunday in os.listdir('logs'):
                print(f"✅ New Sunday folder exists: {new_sunday}")
            else:
                print(f"❌ NEW SUNDAY FOLDER MISSING: {new_sunday}")
            
            handler.stream.close()
            
    finally:
        # Clean up
        os.chdir(original_cwd)
        shutil.rmtree(test_dir, ignore_errors=True)

if __name__ == "__main__":
    test_directory_path_issue()
