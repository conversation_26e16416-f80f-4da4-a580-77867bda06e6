import os
import tempfile
import shutil
from unittest.mock import patch
from datetime import date as real_date, datetime
import logging

# Import the handler directly
from daily_rotating_file_handler import DailyRotatingFileHandler

# Create a mock date class with a today method we can patch
class MockDate(real_date):
    @classmethod
    def today(cls):
        return cls._today_value

def test_directory_path_issue():
    """Test to reproduce and understand the directory path issue"""
    
    # Create a temporary directory for testing
    test_dir = tempfile.mkdtemp()
    original_cwd = os.getcwd()
    os.chdir(test_dir)
    
    try:
        # Create logs directory
        os.makedirs("logs", exist_ok=True)
        
        print(f"Testing in directory: {test_dir}")
        
        # Path to patch: the datetime module in daily_rotating_file_handler
        patch_path = 'daily_rotating_file_handler.datetime.date'
        
        with patch(patch_path, MockDate):
            # Start on Sunday
            MockDate._today_value = datetime(2025, 8, 10).date()  # Sunday
            print(f"Starting on Sunday: {MockDate._today_value}")
            
            # Create handler
            handler = DailyRotatingFileHandler("test.log")
            print(f"Handler created with:")
            print(f"  baseFilename: {handler.baseFilename}")
            print(f"  current_day_dir: {handler.current_day_dir}")
            print(f"  current_date: {handler.current_date}")
            
            # Check what directories exist
            print(f"Directories after Sunday handler creation: {os.listdir('logs') if os.path.exists('logs') else 'No logs dir'}")
            
            # Create a log record and emit it
            record = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Sunday log message", args=(), exc_info=None
            )
            handler.emit(record)
            print(f"After Sunday emit - directories: {os.listdir('logs') if os.path.exists('logs') else 'No logs dir'}")
            
            # Now change to Monday
            MockDate._today_value = datetime(2025, 8, 11).date()  # Monday
            print(f"\nChanging to Monday: {MockDate._today_value}")
            
            # Emit another record - this should trigger day change
            record2 = logging.LogRecord(
                name="test", level=logging.INFO, pathname="", lineno=0,
                msg="Monday log message", args=(), exc_info=None
            )
            handler.emit(record2)
            
            print(f"After Monday emit:")
            print(f"  Handler baseFilename: {handler.baseFilename}")
            print(f"  Handler current_day_dir: {handler.current_day_dir}")
            print(f"  Handler current_date: {handler.current_date}")
            
            # Check directory structure
            print(f"Root logs directories: {os.listdir('logs') if os.path.exists('logs') else 'No logs dir'}")
            
            # Check if there are nested directories
            for item in os.listdir('logs'):
                item_path = os.path.join('logs', item)
                if os.path.isdir(item_path):
                    print(f"Contents of {item}: {os.listdir(item_path)}")
                    # Check for nested directories
                    for subitem in os.listdir(item_path):
                        subitem_path = os.path.join(item_path, subitem)
                        if os.path.isdir(subitem_path):
                            print(f"  Nested directory found: {subitem_path}")
                            print(f"  Contents: {os.listdir(subitem_path)}")
            
            handler.stream.close()
            
    finally:
        # Clean up
        os.chdir(original_cwd)
        shutil.rmtree(test_dir, ignore_errors=True)

if __name__ == "__main__":
    test_directory_path_issue()
