import logging
import time
import os
import shutil
from datetime import datetime, timedelta

# Assuming error.py and daily_rotating_file_handler.py are in the same directory
from error import setup_logger, log # Import setup_logger and log from your error.py
from daily_rotating_file_handler import DailyRotatingFileHandler, DAY_NAMES, LOCK_FILE, HAS_FCNTL # Import HAS_FCNTL directly

# --- Helper functions for testing cleanup directly (for verification) ---
def _get_day_log_directory_test(date_obj, base_filename="logs/test.log"):
    day_name = DAY_NAMES[date_obj.weekday()]
    folder_name = f"{day_name}_{date_obj.day:02d}_{date_obj.month:02d}_{date_obj.year}"
    log_dir = os.path.dirname(base_filename) or "logs"
    return os.path.join(log_dir, folder_name)

def _parse_folder_date_and_day_test(folder_name):
    try:
        parts = folder_name.split("_")
        if len(parts) != 4:
            return None, None
        
        day_name, day, month, year = parts
        if day_name.lower() not in DAY_NAMES:
            return None, None
            
        folder_date = datetime(int(year), int(month), int(day)).date()
        return day_name.lower(), folder_date
    except (ValueError, IndexError):
        return None, None

def _should_cleanup_folder_test(folder_name, current_date):
    day_name, folder_date = _parse_folder_date_and_day_test(folder_name)
    if not day_name or not folder_date:
        return False
    
    current_day_name = DAY_NAMES[current_date.weekday()]
    
    if day_name != current_day_name:
        return False
    
    days_diff = (current_date - folder_date).days
    return days_diff >= 7

def manual_cleanup_trigger(base_logs_dir="logs"):
    print("\n--- Manually triggering cleanup ---")
    if not os.path.exists(base_logs_dir):
        print(f"Log directory {base_logs_dir} does not exist.")
        return

    # Acquire lock for cleanup
    lock_acquired = False
    lockfile = None
    try:
        lockfile = open(LOCK_FILE, "w")
        if HAS_FCNTL:
            try:
                fcntl.flock(lockfile, fcntl.LOCK_EX | fcntl.LOCK_NB)
                lock_acquired = True
            except BlockingIOError:
                print("Another process is already cleaning up. Skipping.")
                return
        else:
            try:
                stat = os.stat(LOCK_FILE)
                if datetime.now().timestamp() - stat.st_mtime < 60:
                    print("Lock file recently created. Another process might be cleaning up. Skipping.")
                    return
            except OSError:
                pass
            lock_acquired = True

        if not lock_acquired:
            return

        now = datetime.now()
        current_day_name = DAY_NAMES[now.weekday()]
        print(f"[MANUAL CLEANUP] Starting cleanup for {current_day_name} logs...")

        for folder in os.listdir(base_logs_dir):
            folder_path = os.path.join(base_logs_dir, folder)
            if not os.path.isdir(folder_path):
                continue
                
            if _should_cleanup_folder_test(folder, now.date()):
                try:
                    shutil.rmtree(folder_path)
                    print(f"[MANUAL CLEANUP] Removed old {current_day_name} log folder: {folder_path}")
                except Exception as e:
                    print(f"[MANUAL CLEANUP WARNING] Could not remove {folder_path}: {e}")

        print(f"[MANUAL CLEANUP] Cleanup completed for {current_day_name} logs")

    except Exception as e:
        print(f"[MANUAL CLEANUP ERROR] Cleanup failed: {e}")
    finally:
        if lockfile:
            try:
                if HAS_FCNTL and lock_acquired:
                    fcntl.flock(lockfile, fcntl.LOCK_UN)
                lockfile.close()
            except Exception as e:
                print(f"[MANUAL CLEANUP WARNING] Could not release lock: {e}")

# --- Main Test Logic ---
def run_test_scenario():
    # Clean up any previous test logs and lock file
    if os.path.exists("logs"):
        shutil.rmtree("logs")
    os.makedirs("logs", exist_ok=True)
    if os.path.exists(LOCK_FILE):
        os.remove(LOCK_FILE)

    print("\n--- Starting Logging System Test ---")

    # Setup loggers
    main_logger = setup_logger("main", "logs/main.log")
    docker_logger = setup_logger("docker_restarter", "logs/docker_restarter.log")
    task_logger = setup_logger("Task", "logs/Task.log")

    loggers = {
        "main": main_logger,
        "docker_restarter": docker_logger,
        "Task": task_logger
    }

    # Simulate logging for a few days
    print("\n--- Simulating logging for 10 days ---")
    for i in range(10):
        current_simulated_date = datetime.now().date() + timedelta(days=i)
        print(f"\nSimulating Day: {current_simulated_date.strftime('%Y-%m-%d (%A)')}")

        # In a real long-running app, the DailyRotatingFileHandler.emit() would handle this automatically.
        # For this test, we simulate the day change by forcing the handler to rotate.
        # We need to get the actual handler instance and call its emit method.
        for logger_name, logger_obj in loggers.items():
            # Get the DailyRotatingFileHandler instance
            file_handler = None
            for handler in logger_obj.handlers:
                if isinstance(handler, DailyRotatingFileHandler):
                    file_handler = handler
                    break
            
            if file_handler: # If a DailyRotatingFileHandler is found
                # Force the handler to check for day change and rotate if necessary
                # This is a bit of a hack for testing, as emit is usually called by logger.log()
                # but we need to ensure the handler\"s internal state is updated for the next day
                file_handler.emit(logging.LogRecord(logger_name, logging.INFO, __file__, 1, "", [], None))

        log(loggers["main"], "info", f"Log entry for main application on {current_simulated_date}")
        log(loggers["docker_restarter"], "info", f"Docker restarter activity on {current_simulated_date}")
        log(loggers["Task"], "warning", f"Task processing warning on {current_simulated_date}")

        # Verify log file paths after logging
        for logger_name, logger_obj in loggers.items():
            # Get the current file path from the handler
            current_file_path = None
            for handler in logger_obj.handlers:
                if isinstance(handler, DailyRotatingFileHandler):
                    current_file_path = handler.baseFilename
                    break

            if current_file_path:
                print(f"  Current log file for {logger_name}: {current_file_path}")
                if os.path.exists(current_file_path):
                    print(f"  Log file exists: {current_file_path}")
                else:
                    print(f"  ERROR: Log file DOES NOT exist: {current_file_path}")
            else:
                print(f"  ERROR: No DailyRotatingFileHandler found for {logger_name}")

        # Simulate time passing (not strictly necessary for handler\"s internal logic, but good for demo)
        time.sleep(0.1) # Small delay

    print("\n--- Verifying log directories after simulation ---")
    for root, dirs, files in os.walk("logs"):
        for d in dirs:
            print(f"Found directory: {os.path.join(root, d)}")

    # Manually trigger cleanup to show its effect after the simulation
    manual_cleanup_trigger()

    print("\n--- Verifying log directories after manual cleanup ---")
    for root, dirs, files in os.walk("logs"):
        for d in dirs:
            print(f"Found directory: {os.path.join(root, d)}")

    print("\n--- Test Scenario Complete ---")
    print("Please manually inspect the \"logs\" directory to confirm the daily rotation and weekly deletion.")
    print("You should see log directories for the last 7 days, organized by day of the week and date.")
    print("Old directories for the current day of the week (from 7+ days ago) should be removed.")

if __name__ == "__main__":
    run_test_scenario()