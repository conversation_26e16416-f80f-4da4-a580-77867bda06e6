import time
from unittest.mock import patch, MagicMock
import daily_rotating_file_handler
from error import setup_logger
import datetime
from datetime import date as real_date

# Force Unix-like locking for simplicity in testing (assuming fcntl is available; adjust if on Windows)
daily_rotating_file_handler.HAS_FCNTL = False

# Setup the logger using the existing system
logger = setup_logger("test", "logs/test.log")

# Create a mock date class with a today method we can patch
class MockDate(real_date):
    @classmethod
    def today(cls):
        return cls._today_value

# Path to patch: the datetime module in daily_rotating_file_handler
patch_path = 'daily_rotating_file_handler.datetime.date'

# Starting date: Sunday, August 10, 2025
start_date = datetime.date(2025, 8, 10)

# Simulation parameters
# Each "day" lasts 300 seconds (5 minutes) in real time
interval_per_day = 30  # 1 minutes; adjust to smaller (e.g., 30) for faster testing
num_days = 14  # Simulate 14 days to cover two weeks and trigger cleanup on the second Sunday

# Number of log messages per "day" (with small sleeps between them)
logs_per_day = 3
sleep_between_logs = 5  # seconds between logs within a "day"

with patch(patch_path, MockDate):
    current_date = start_date
    for day in range(num_days):
        # Set the class variable that the today method will return
        MockDate._today_value = current_date
        weekday = current_date.strftime('%A').lower()
        print(f"Simulating {weekday.capitalize()} {current_date} - writing logs to logs/{weekday}_{current_date.day:02d}_{current_date.month:02d}_{current_date.year}/test.log")

        # Write some log messages for this "day"
        for i in range(logs_per_day):
            logger.info(f"Test log message on {current_date} - #{i}")
            time.sleep(sleep_between_logs)

        # Sleep for the remaining "day" duration (simulating time passing)
        remaining_sleep = interval_per_day - (logs_per_day * sleep_between_logs)
        if remaining_sleep > 0:
            print(f"Sleeping {remaining_sleep} seconds to simulate end of {weekday.capitalize()}...")
            time.sleep(remaining_sleep)

        # Advance to the next "day"
        current_date += datetime.timedelta(days=1)

        # On day change, the next emit will trigger rotation and potential cleanup
        print(f"Advancing to next day: {current_date}")

print("Test simulation completed. Check the 'logs/' directory for created/deleted folders and files.")
print("Expected behavior: Daily rotation every ~1 minutes, and cleanup of the first Sunday's folder on the second Sunday.")