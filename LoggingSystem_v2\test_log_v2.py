import time
import os
import shutil
from unittest.mock import patch, MagicMock
import daily_rotating_file_handler
from error import setup_logger, log
import datetime
from datetime import date as real_date

# Force Unix-like locking for simplicity in testing (assuming fcntl is available; adjust if on Windows)
daily_rotating_file_handler.HAS_FCNTL = False

# Create a mock date class with a today method we can patch
class MockDate(real_date):
    @classmethod
    def today(cls):
        return cls._today_value

# Path to patch: the datetime module in daily_rotating_file_handler
patch_path = 'daily_rotating_file_handler.datetime.date'

# Starting date: Sunday, August 10, 2025
start_date = datetime.date(2025, 8, 10)

# Simulation parameters
interval_per_day = 30  # seconds per "day" - adjust for faster/slower testing
num_days = 14  # Simulate 14 days to cover two weeks and trigger cleanup on the second Sunday
logs_per_day = 2  # Number of log messages per logger per "day"
sleep_between_logs = 3  # seconds between individual log messages
sleep_between_loggers = 1  # seconds between different loggers

# Clean up any existing logs directory for a fresh start
if os.path.exists("logs"):
    print("Cleaning up existing logs directory...")
    shutil.rmtree("logs")
    time.sleep(1)  # Give filesystem time to clean up

print("=== Comprehensive Multi-Logger Daily Rotation Test ===")
print(f"Testing {num_days} days with {interval_per_day}s per day")
print(f"Each logger will write {logs_per_day} messages per day")
print()

# Create multiple loggers with different purposes
loggers = {
    "main": setup_logger("main", "main.log"),
    "docker": setup_logger("docker_restarter", "docker_restarter.log"),
    "emulator": setup_logger("emulator", "emulator.log"),
    "task": setup_logger("task_manager", "task_manager.log"),
    "device": setup_logger("device_manager", "device_manager.log")
}

print("Created loggers:")
for name, logger_obj in loggers.items():
    print(f"  - {name}: {logger_obj.name}")
print()

def get_log_directories():
    """Get all log directories in the logs folder"""
    if not os.path.exists("logs"):
        return []
    return [d for d in os.listdir("logs") if os.path.isdir(os.path.join("logs", d)) and not d.startswith('.')]

def get_log_files_in_directory(directory):
    """Get all log files in a specific directory"""
    dir_path = os.path.join("logs", directory)
    if not os.path.exists(dir_path):
        return []
    return [f for f in os.listdir(dir_path) if f.endswith('.log')]

def print_directory_status():
    """Print current status of log directories and files"""
    directories = get_log_directories()
    if not directories:
        print("  No log directories found")
        return

    for directory in sorted(directories):
        files = get_log_files_in_directory(directory)
        print(f"  📁 {directory}/ ({len(files)} files)")
        for file in sorted(files):
            file_path = os.path.join("logs", directory, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                print(f"    📄 {file} ({size} bytes)")

# Mock telegram to avoid actual API calls during testing
with patch('error.send_telegram_message', return_value={'ok': True}):
    with patch(patch_path, MockDate):
        current_date = start_date

        for day in range(num_days):
            # Set the class variable that the today method will return
            MockDate._today_value = current_date
            weekday = current_date.strftime('%A').lower()

            print(f"\n🗓️  Day {day + 1}: {weekday.capitalize()} {current_date}")
            print(f"Expected directory: logs/{weekday}_{current_date.day:02d}_{current_date.month:02d}_{current_date.year}/")

            # Write log messages for each logger
            for logger_name, logger_obj in loggers.items():
                print(f"  📝 Writing logs for {logger_name}...")

                for i in range(logs_per_day):
                    # Create different types of log messages for variety
                    if logger_name == "main":
                        message = f"Main application event #{i+1} on {current_date}"
                    elif logger_name == "docker":
                        message = f"Docker container restart check #{i+1} - Status: OK"
                    elif logger_name == "emulator":
                        message = f"Emulator device_{i+1} health check - Connected"
                    elif logger_name == "task":
                        message = f"Task queue processing item #{i+1} - Completed"
                    elif logger_name == "device":
                        message = f"Device manager scan #{i+1} - Found {i+3} devices"

                    # Use the log function with send_on_telegram=False for testing
                    log(logger_obj, "info", message, send_on_telegram=False)
                    time.sleep(sleep_between_logs)

                time.sleep(sleep_between_loggers)

            # Show current directory status
            print("  📊 Current log structure:")
            print_directory_status()

            # Sleep for the remaining "day" duration
            total_log_time = len(loggers) * (logs_per_day * sleep_between_logs + sleep_between_loggers)
            remaining_sleep = max(0, interval_per_day - total_log_time)

            if remaining_sleep > 0:
                print(f"  ⏳ Sleeping {remaining_sleep}s to complete the day...")
                time.sleep(remaining_sleep)

            # Advance to the next "day"
            current_date += datetime.timedelta(days=1)

            # Check for cleanup on Sunday (when old Sunday folders should be deleted)
            if weekday == "sunday" and day > 6:  # Second Sunday and beyond
                print(f"  🧹 Cleanup check: Old Sunday folders should be removed")

print("\n" + "="*60)
print("🎉 COMPREHENSIVE TEST COMPLETED!")
print("="*60)

print("\n📊 Final Log Structure:")
print_directory_status()

print(f"\n📈 Test Summary:")
print(f"  • Simulated {num_days} days of logging")
print(f"  • Used {len(loggers)} different loggers")
print(f"  • Each logger wrote {logs_per_day} messages per day")
print(f"  • Total messages per day: {len(loggers) * logs_per_day}")
print(f"  • Total messages overall: {num_days * len(loggers) * logs_per_day}")

directories = get_log_directories()
print(f"  • Final directory count: {len(directories)}")

if len(directories) <= 7:
    print("  ✅ Cleanup working correctly (≤7 directories)")
else:
    print("  ⚠️  More than 7 directories - cleanup may not be working")

print(f"\n📁 Expected behavior:")
print(f"  • Daily rotation: Each day creates new directories")
print(f"  • Weekly cleanup: Old directories (>7 days) are removed")
print(f"  • Multiple files: Each logger creates its own .log file")
print(f"  • Directory naming: {weekday}_DD_MM_YYYY format")

print(f"\n🔍 To verify manually:")
print(f"  1. Check 'logs/' directory for proper structure")
print(f"  2. Verify each day directory contains all 5 log files")
print(f"  3. Confirm old Sunday directory was deleted on second Sunday")
print(f"  4. Check log file contents for proper messages")